-- Update existing sessions with 'assigned' status to 'pending' status
-- This migration fixes the status inconsistency after changing from 'assigned' to 'pending'

UPDATE sessions 
SET status = 'pending' 
WHERE status = 'assigned';

-- Log the number of updated records
DO $$
DECLARE
    updated_count INTEGER;
BEGIN
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Updated % sessions from assigned to pending status', updated_count;
END $$;
